from abc import ABC
from typing import List, Optional, Tuple
from mailbot.models import (
    AlgorithmCategoryScore,
    AlgorithmDefinition,
    MessageCategory,
    SenderProfile,
    UserMailBotProfile,
)


class BaseAlgorithm(ABC):
    def __init__(self, algo_def: AlgorithmDefinition):
        self.algo_def = algo_def

    @property
    def version_code(self) -> str:
        return "v1"

    def should_send_fts_overlay(self, sender_profile: SenderProfile) -> bool:
        return False

    def should_show_lucene_overlay(self, message_categories: List["MessageCategory"]) -> bool:
        return True

    def should_whitelist_default_action(
        self, user_mailbot_profile: UserMailBotProfile, message_categories: List[MessageCategory]
    ) -> bool:
        return False

    def should_whitelist_read_fraction_action(
        self, sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]
    ) -> Tuple[bool, Optional[bool]]:
        open_rate = self.get_open_rate(sender_profile, mailbot_profile_id, message_categories)
        should_whitelist = open_rate >= 0.9
        include_in_digest = open_rate >= 0.3 and not should_whitelist
        return should_whitelist, include_in_digest

    def have_important_categories(self, message_categories: List[MessageCategory]) -> bool:
        return self.get_sum_category_score(message_categories) >= 2

    def get_sum_category_score(self, message_categories: List[MessageCategory]) -> int:
        return sum(self.get_category_score(category) for category in message_categories)

    def get_category_score(self, category: MessageCategory) -> int:
        try:
            return AlgorithmCategoryScore.objects.get(algorithm=self.algo_def, category__name=category.name).score
        except AlgorithmCategoryScore.DoesNotExist:
            return category.score

    def get_read_fraction_counts(
        self, sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]
    ) -> int:
        return sender_profile.total_count

    def get_open_rate(self, sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]) -> float:
        ## To not count the current message and total_count will always be > 1
        return sender_profile.read_count / (sender_profile.total_count - 1)
