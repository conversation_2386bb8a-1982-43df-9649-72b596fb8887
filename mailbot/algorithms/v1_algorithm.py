from typing import List
from mailbot.algorithms.base import BaseAlgorithm
from mailbot.models import Message<PERSON>ate<PERSON><PERSON>, UserMailBotProfile


class V1Algorithm(BaseAlgorithm):
    VERSION_CODE = "v1"

    @property
    def version_code(self) -> str:
        return self.VERSION_CODE

    @property
    def should_whitelist_default_action(
        self, user_mailbot_profile: UserMailBotProfile, message_categories: List[MessageCategory]
    ) -> bool:
        first_time_sender_treatment = user_mailbot_profile.preferences["first_time_sender_treatment"]
        return first_time_sender_treatment == "inbox"
