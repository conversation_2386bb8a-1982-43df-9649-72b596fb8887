import logging
from typing import Any, Dict, List

from mailbot.algorithms.base import BaseAlgorithm
from mailbot.utils.label_engine.filters.base import BaseStateFilter
from mailbot.utils.message_parser import ParsedMessage
from mailbot.utils.rule_engine.rules.base import BaseRule

logger = logging.getLogger(__name__)


class LuceneFilter(BaseStateFilter):
    @staticmethod
    def get_category_tags(parsed_message: ParsedMessage) -> List[str]:
        """
        Retrieves the category tags for an email based on the evaluation of filtering rules using lucene.

        Args:
           parsed_message: The parsed message

        Returns:
            List[str]: A list of category tags that the email matches.
        """
        base_rule = BaseRule()
        rule_expression = "((PasswordFilter OR CalendarEventFilter) OR ((OtpFilter OR SecurityFilter) OR (BankStatementFilter OR (PaymentsFilter OR OrderTicketFilter))))"
        base_rule.set_rules(rule_expression)
        category_tags: List[str] = []
        if base_rule.evaluate(parsed_message=parsed_message):
            for filter_item in base_rule.passed_filters:
                category_tags.append(filter_item.tag)
        if category_tags:
            logger.info(f"Category tags found are {category_tags}")
        return category_tags

    @staticmethod
    def evaluate(parsed_message: ParsedMessage, algorithm: BaseAlgorithm, **kwargs) -> Dict[str, Any]:
        if algorithm.should_show_lucene_overlay(kwargs.get("message_categories", [])):
            if category_tags := LuceneFilter.get_category_tags(parsed_message):
                return {"condition": True, "category_tags": category_tags}
        return {"condition": False}
