import logging
from typing import Any, Dict
from django.db.models import Count
from applications.utils.email import get_normalized_email
from mailbot.algorithms.base import BaseAlgorithm
from mailbot.models import Message, SenderProfile, UserMailBotProfile
from mailbot.utils.label_engine.filters.base import BaseStateFilter
from mailbot.utils.message_parser import ParsedMessage
from constance import config as constance_config


logger = logging.getLogger(__name__)


class ReadFractionFilter(BaseStateFilter):
    """
    A filter that determines whether the read fraction filter should be applied to emails
    based on the sender's email activity within specified categories.

    This filter checks if a sender has sent emails in specific categories above
    a defined threshold (`READ_FRACTION_FILTER_THRESHOLD`).
    """

    @staticmethod
    def evaluate(parsed_message: ParsedMessage, algorithm: BaseAlgorithm, **kwargs) -> Dict[str, Any]:
        sender_email = parsed_message.from_name_email[1]
        normalized_sender_email = get_normalized_email(sender_email)
        sender_profile = SenderProfile.objects.get(
            normalized_sender_email=normalized_sender_email,
            user_mailbot_profile_id=parsed_message.user_mailbot_profile_id,
        )
        message_categories = kwargs.get("message_categories", [])
        mailbot_profile_id = parsed_message.user_mailbot_profile_id
        message_counts = algorithm.get_read_fraction_counts(sender_profile, mailbot_profile_id, message_categories)
        return ReadFractionFilter.base_filter(message_counts=message_counts)

    @staticmethod
    def base_filter(
        message_counts: int,
    ):
        """
        Checks if a sender has sent enough emails in the given categories to meet the threshold.

        This function checks if the sender has sent at least `READ_FRACTION_FILTER_THRESHOLD` emails
        that belong exclusively to the given categories. The filtering process ensures that only
        messages containing exactly all the specified categories (and no extra ones) are considered.

        Args:
            sender_profile (SenderProfile): The sender profile being evaluated.
            message_counts (int): The number of messages sent by the sender that belong to the specified categories.
            message_categories (List[int], optional): A list of category IDs to check against.

        Returns:
            Dict[str, bool]: A dictionary with a "condition" key indicating whether all
            specified categories meet the threshold.
        """
        meets_threshold = message_counts >= constance_config.READ_FRACTION_FILTER_THRESHOLD
        logger.info(f"Read Fraction Filter - Total Count: {message_counts}, " f"Meets Threshold: {meets_threshold}")
        return {"condition": meets_threshold}
