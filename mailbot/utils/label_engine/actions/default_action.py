from typing import Any, Dict

from mailbot.algorithms.base import BaseAlgorithm
from mailbot.models import MailBotGeneric<PERSON>abel, UserMailBotProfile
from mailbot.utils.defaults import MailBotMessageLabelReason
from mailbot.utils.label_engine.actions.base import BaseStateAction
from mailbot.utils.message_parser import ParsedMessage


class DefaultAction(BaseStateAction):
    @staticmethod
    def execute(
        parsed_message: ParsedMessage, filter_result: Dict[str, Any], algorithm: BaseAlgorithm, **kwargs
    ) -> Dict[str, Any]:
        user_mailbot_profile = UserMailBotProfile.objects.get(id=parsed_message.user_mailbot_profile_id)
        message_categories = kwargs.get("message_categories", [])
        should_whitelist = algorithm.should_whitelist_default_action(user_mailbot_profile, message_categories)
        return DefaultAction.base_action(should_whitelist)

    @staticmethod
    def base_action(should_whitelist: bool) -> Dict[str, Any]:
        action_result = {"message_labelled_due_to": MailBotMessageLabelReason.NO_FILTER.value}
        if should_whitelist:
            action_result["label_name"] = MailBotGenericLabel.WHITE_LIST.value
        else:
            action_result["label_name"] = MailBotGenericLabel.ZAPPED.value
        return action_result
