"""
MailBot Algorithm Integration Examples

This file shows practical examples of how to integrate the new algorithm engine
into your existing codebase.
"""

# =============================================================================
# EXAMPLE 1: Basic Message Processing (Most Common)
# =============================================================================

def example_1_basic_message_processing():
    """Example: Basic message processing with algorithm classification."""
    
    # OLD WAY (Before refactoring)
    def process_message_old(parsed_message, user_profile, message_categories):
        from mailbot.utils.label_engine.actions.read_fraction_action import ReadFractionAction
        
        # Get sender profile (you'd implement this)
        sender_profile = get_sender_profile(parsed_message.sender_email)
        
        # Execute algorithm
        result = ReadFractionAction.base_action(
            sender_profile=sender_profile,
            algo_version=user_profile.algo_version,
            parsed_message=parsed_message,
            message_categories=message_categories
        )
        
        return result
    
    # NEW WAY (After refactoring) - Drop-in replacement
    def process_message_new(parsed_message, user_profile, message_categories):
        from mailbot.algorithms.engine import process_message_with_algorithm
        
        # Get sender profile (same as before)
        sender_profile = get_sender_profile(parsed_message.sender_email)
        
        # Execute with new engine (same interface!)
        result = process_message_with_algorithm(
            message=parsed_message,
            profile=user_profile,
            message_categories=message_categories,
            sender_profile=sender_profile  # Pass as kwarg
        )
        
        return result
    
    # ENHANCED WAY (Using dynamic rules)
    def process_message_enhanced(parsed_message, user_profile, message_categories):
        from mailbot.rules.engine import process_message_with_enhanced_rules
        
        # Get sender profile
        sender_profile = get_sender_profile(parsed_message.sender_email)
        
        # Use enhanced rules with YAML configuration
        result = process_message_with_enhanced_rules(
            algorithm_version=user_profile.algo_version,
            parsed_message=parsed_message,
            rule_set_name="enhanced",  # Use enhanced rule set
            message_categories=message_categories,
            sender_profile=sender_profile
        )
        
        return result


# =============================================================================
# EXAMPLE 2: Webhook Processing
# =============================================================================

def example_2_webhook_processing():
    """Example: Email webhook processing with algorithm integration."""
    
    # OLD WAY
    def handle_email_webhook_old(webhook_data):
        # Parse webhook
        parsed_message = parse_webhook_message(webhook_data)
        user_profile = get_user_profile(parsed_message.user_mailbot_profile_id)
        message_categories = extract_categories(parsed_message)
        
        # Manual algorithm version handling
        from mailbot.utils.label_engine.actions.read_fraction_action import ReadFractionAction
        
        if user_profile.algo_version == "v3":
            # V3 specific logic
            result = ReadFractionAction.base_action(
                sender_profile=get_sender_profile(parsed_message.sender_email),
                algo_version="v3",
                parsed_message=parsed_message,
                message_categories=message_categories
            )
        elif user_profile.algo_version == "v2":
            # V2 specific logic
            result = ReadFractionAction.base_action(
                sender_profile=get_sender_profile(parsed_message.sender_email),
                algo_version="v2",
                parsed_message=parsed_message,
                message_categories=message_categories
            )
        else:
            # Default to v1
            result = ReadFractionAction.base_action(
                sender_profile=get_sender_profile(parsed_message.sender_email),
                algo_version="v1",
                parsed_message=parsed_message,
                message_categories=message_categories
            )
        
        # Apply result
        apply_classification_result(parsed_message, result)
        return result
    
    # NEW WAY
    def handle_email_webhook_new(webhook_data):
        # Parse webhook (same as before)
        parsed_message = parse_webhook_message(webhook_data)
        user_profile = get_user_profile(parsed_message.user_mailbot_profile_id)
        message_categories = extract_categories(parsed_message)
        
        # Automatic algorithm selection and execution
        from mailbot.algorithms.engine import get_engine
        
        engine = get_engine()
        result = engine.process_message_legacy(
            parsed_message=parsed_message,
            profile=user_profile,
            message_categories=message_categories,
            sender_profile=get_sender_profile(parsed_message.sender_email)
        )
        
        # Apply result (same as before)
        apply_classification_result(parsed_message, result)
        return result


# =============================================================================
# EXAMPLE 3: Batch Processing
# =============================================================================

def example_3_batch_processing():
    """Example: Batch processing multiple messages efficiently."""
    
    # OLD WAY
    def process_message_batch_old(messages):
        from mailbot.utils.label_engine.actions.read_fraction_action import ReadFractionAction
        
        results = []
        for message_data in messages:
            parsed_message = message_data['parsed_message']
            user_profile = message_data['user_profile']
            categories = message_data['categories']
            sender_profile = message_data['sender_profile']
            
            # Individual algorithm calls (inefficient)
            result = ReadFractionAction.base_action(
                sender_profile=sender_profile,
                algo_version=user_profile.algo_version,
                parsed_message=parsed_message,
                message_categories=categories
            )
            results.append(result)
        
        return results
    
    # NEW WAY
    def process_message_batch_new(messages):
        from mailbot.algorithms.engine import get_engine
        
        # Reuse engine instance for efficiency
        engine = get_engine()
        results = []
        
        for message_data in messages:
            parsed_message = message_data['parsed_message']
            user_profile = message_data['user_profile']
            categories = message_data['categories']
            sender_profile = message_data['sender_profile']
            
            # Efficient batch processing with caching
            result = engine.process_message_legacy(
                parsed_message=parsed_message,
                profile=user_profile,
                message_categories=categories,
                sender_profile=sender_profile
            )
            results.append(result)
        
        return results


# =============================================================================
# EXAMPLE 4: Feature Flag Integration (Safest Migration)
# =============================================================================

def example_4_feature_flag_integration():
    """Example: Using feature flags for safe migration."""
    
    def process_message_with_feature_flags(parsed_message, user_profile, message_categories):
        from django.conf import settings
        from mailbot.compatibility.legacy_adapter import execute_algorithm_with_compatibility
        
        # This function automatically handles:
        # - Feature flag checking
        # - Gradual rollout percentage
        # - Comparison logging
        # - Fallback to legacy system
        
        result = execute_algorithm_with_compatibility(
            parsed_message=parsed_message,
            sender_profile=get_sender_profile(parsed_message.sender_email),
            message_categories=message_categories,
            algo_version=user_profile.algo_version
        )
        
        return result


# =============================================================================
# EXAMPLE 5: Service Layer Integration
# =============================================================================

def example_5_service_layer():
    """Example: Integrating into a service layer architecture."""
    
    class EmailClassificationService:
        """Service for email classification using the new algorithm engine."""
        
        def __init__(self):
            from mailbot.algorithms.engine import get_engine
            self.algorithm_engine = get_engine()
        
        def classify_email(self, email_data):
            """Classify an email using the algorithm engine."""
            # Parse email data
            parsed_message = self.parse_email(email_data)
            user_profile = self.get_user_profile(parsed_message.user_mailbot_profile_id)
            categories = self.extract_categories(parsed_message)
            sender_profile = self.get_sender_profile(parsed_message.sender_email)
            
            # Classify using new engine
            result = self.algorithm_engine.process_message_legacy(
                parsed_message=parsed_message,
                profile=user_profile,
                message_categories=categories,
                sender_profile=sender_profile
            )
            
            return result
        
        def classify_batch(self, email_batch):
            """Classify multiple emails efficiently."""
            results = []
            for email_data in email_batch:
                result = self.classify_email(email_data)
                results.append(result)
            return results
        
        def parse_email(self, email_data):
            """Parse email data into ParsedMessage."""
            # Your email parsing logic here
            pass
        
        def get_user_profile(self, profile_id):
            """Get user profile by ID."""
            # Your profile retrieval logic here
            pass
        
        def extract_categories(self, parsed_message):
            """Extract categories from parsed message."""
            # Your category extraction logic here
            pass
        
        def get_sender_profile(self, sender_email):
            """Get sender profile by email."""
            # Your sender profile logic here
            pass


# =============================================================================
# EXAMPLE 6: Django View Integration
# =============================================================================

def example_6_django_views():
    """Example: Integrating into Django views."""
    
    from django.http import JsonResponse
    from django.views.decorators.csrf import csrf_exempt
    from django.views.decorators.http import require_http_methods
    import json
    
    @csrf_exempt
    @require_http_methods(["POST"])
    def classify_email_endpoint(request):
        """API endpoint for email classification."""
        try:
            # Parse request
            data = json.loads(request.body)
            email_data = data.get('email_data')
            
            # Use the service layer
            service = EmailClassificationService()
            result = service.classify_email(email_data)
            
            return JsonResponse({
                'success': True,
                'classification': result
            })
            
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=500)
    
    @csrf_exempt
    @require_http_methods(["POST"])
    def webhook_handler(request):
        """Webhook handler for incoming emails."""
        try:
            # Parse webhook data
            webhook_data = json.loads(request.body)
            
            # Process with new algorithm engine
            from mailbot.algorithms.engine import process_message_with_algorithm
            
            parsed_message = parse_webhook_message(webhook_data)
            user_profile = get_user_profile(parsed_message.user_mailbot_profile_id)
            categories = extract_categories(parsed_message)
            
            result = process_message_with_algorithm(
                message=parsed_message,
                profile=user_profile,
                message_categories=categories,
                sender_profile=get_sender_profile(parsed_message.sender_email)
            )
            
            # Apply classification
            apply_classification_result(parsed_message, result)
            
            return JsonResponse({'success': True, 'result': result})
            
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)


# =============================================================================
# EXAMPLE 7: Celery Task Integration
# =============================================================================

def example_7_celery_tasks():
    """Example: Integrating into Celery background tasks."""
    
    from celery import shared_task
    
    @shared_task
    def process_email_async(email_data):
        """Asynchronous email processing task."""
        from mailbot.algorithms.engine import process_message_with_algorithm
        
        # Parse email data
        parsed_message = parse_email_data(email_data)
        user_profile = get_user_profile(parsed_message.user_mailbot_profile_id)
        categories = extract_categories(parsed_message)
        sender_profile = get_sender_profile(parsed_message.sender_email)
        
        # Process with new algorithm engine
        result = process_message_with_algorithm(
            message=parsed_message,
            profile=user_profile,
            message_categories=categories,
            sender_profile=sender_profile
        )
        
        # Apply result
        apply_classification_result(parsed_message, result)
        
        return result
    
    @shared_task
    def process_email_batch_async(email_batch):
        """Asynchronous batch processing task."""
        from mailbot.algorithms.engine import get_engine
        
        engine = get_engine()
        results = []
        
        for email_data in email_batch:
            parsed_message = parse_email_data(email_data)
            user_profile = get_user_profile(parsed_message.user_mailbot_profile_id)
            categories = extract_categories(parsed_message)
            sender_profile = get_sender_profile(parsed_message.sender_email)
            
            result = engine.process_message_legacy(
                parsed_message=parsed_message,
                profile=user_profile,
                message_categories=categories,
                sender_profile=sender_profile
            )
            
            results.append(result)
        
        return results


# =============================================================================
# HELPER FUNCTIONS (You'll need to implement these based on your codebase)
# =============================================================================

def get_sender_profile(sender_email):
    """Get sender profile - implement based on your data model."""
    # Example implementation:
    # from mailbot.models import SenderProfile
    # return SenderProfile.objects.get_or_create(email=sender_email)[0]
    pass

def get_user_profile(profile_id):
    """Get user profile - implement based on your data model."""
    # Example implementation:
    # from mailbot.models import UserMailBotProfile
    # return UserMailBotProfile.objects.get(id=profile_id)
    pass

def parse_webhook_message(webhook_data):
    """Parse webhook data into ParsedMessage."""
    # Implement based on your webhook format
    pass

def extract_categories(parsed_message):
    """Extract categories from parsed message."""
    # Implement based on your category system
    pass

def apply_classification_result(parsed_message, result):
    """Apply classification result to the message."""
    # Implement based on your message handling system
    pass

def parse_email_data(email_data):
    """Parse email data into ParsedMessage."""
    # Implement based on your email data format
    pass


# =============================================================================
# USAGE EXAMPLES
# =============================================================================

if __name__ == "__main__":
    # These are just examples - you'll need to adapt them to your specific use case
    print("MailBot Algorithm Integration Examples")
    print("See the functions above for integration patterns.")
    print("Adapt these examples to your specific codebase structure.")
